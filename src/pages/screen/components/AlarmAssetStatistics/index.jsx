/*
 * @Author: ss
 * @Date: 2025-06-04 15:36:11
 * @LastEditors: @LastEditedBy
 * @LastEditTime: 2025-07-30 16:25:03
 * @FilePath: /水利厅运维/src/pages/screen/components/AlarmAssetStatistics/index.jsx
 */

import React, { memo } from 'react';
import styles from './index.less';
import Empty from '../Empty';
import ProgressRow from './ProgressRow';
import { useRequest } from 'phooks';
import Card from '../Card';
import { getAlarmAssetStatistics } from '../../serivers';

const AlarmAssetStatistics = memo(props => {
  const { height } = props;
  const { data: dataSource = [], loading } = useRequest(getAlarmAssetStatistics, {
    formatResult: res => {
      const alarmAssetList = res.data?.alarmAssetList || [];
      return alarmAssetList
        .map(v => ({ value: v.alarmCount, name: v.assetCategory }))
        .sort((a, b) => b.value - a.value); // 按数值大小降序排序
    },
  });

  const dataMaxValue = Math.max(...dataSource.map(item => item.value));
  const progressMaxValue = dataMaxValue / 0.8;

  return (
    <Card title="告警资产统计" height={height} loading={loading}>
      <div className={styles.container}>
        {dataSource.length > 0 ? (
          <div className={styles.alarmList}>
            {dataSource.map((item, index) => {
              const progress = (item.value / progressMaxValue) * 100;
              return (
                <div className={styles.alarmItem} key={item.name}>
                  <span className={styles.alarmItemIndex}>{index + 1}</span>
                  <span className={styles.alarmItemName}>{item.name}</span>
                  <ProgressRow value={item.value} progress={progress} />
                </div>
              );
            })}
          </div>
        ) : (
          <Empty isEmpty />
        )}
      </div>
    </Card>
  );
});

export default AlarmAssetStatistics;
