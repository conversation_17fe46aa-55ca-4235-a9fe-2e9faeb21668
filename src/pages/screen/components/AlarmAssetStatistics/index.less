.container {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .alarmList {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow-y: auto;
    padding-right: 4px;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(65, 182, 255, 0.6);
      border-radius: 2px;

      &:hover {
        background: rgba(65, 182, 255, 0.8);
      }
    }
  }
  .alarmItem {
    display: flex;
    align-items: center;
    color: #fff;
    gap: 8px;
    min-height: 32px;
    flex-shrink: 0;

    .alarmItemIndex {
      position: relative;
      width: 24px;
      height: 24px;
      background: #094479;
      text-align: center;
      line-height: 24px;
      font-size: 12px;

      &:before,
      &:after {
        content: '';
        display: block;
        width: 1px;
        height: 5px;
        background: #fff;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }

      &:before {
        left: 0;
      }
      &:after {
        right: 0;
      }
    }

    .alarmItemName {
      width: 68px;
      font-size: 14px;
      line-height: 12px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      min-width: 0;
    }

    .alaramProgress {
      flex: 1;
      min-width: 74px;
      height: 8px;
      background: #052f5e;
      position: relative;

      &:before {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: var(--progress-width, 0);
        height: 100%;
        background: linear-gradient(to right, #00527d 0%, #00efff 100%);
      }
    }

    .alarmValue {
      font-size: 14px;
      text-align: right;
      color: #ffffff;
      line-height: 18px;
      width: 70px; // 增加宽度以支持5位数字显示
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      position: relative;
      cursor: pointer;

      // 鼠标悬停时显示完整内容的tooltip
      &:hover {
        &:after {
          content: attr(title);
          position: absolute;
          top: -30px;
          right: 0;
          background: rgba(0, 0, 0, 0.8);
          color: #fff;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          white-space: nowrap;
          z-index: 1000;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        &:before {
          content: '';
          position: absolute;
          top: -6px;
          right: 10px;
          width: 0;
          height: 0;
          border-left: 4px solid transparent;
          border-right: 4px solid transparent;
          border-top: 4px solid rgba(0, 0, 0, 0.8);
          z-index: 1000;
        }
      }
    }
  }
}
